import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ForgotPasswordPage from '../ForgotPasswordPage';

// Mock the services
vi.mock('../../../services', () => ({
  initializeServices: vi.fn(),
}));

// Mock the components
vi.mock('../../../components/common/AnimationBox', () => ({
  default: ({ children, className }) => <div className={className}>{children}</div>,
}));

vi.mock('../../../components/layout/Header', () => ({
  default: () => <div data-testid="header">Header</div>,
}));

vi.mock('../../../components/ui', () => ({
  ReCaptcha: vi.forwardRef(({ onChange }, ref) => (
    <div data-testid="recaptcha">
      <button onClick={() => onChange('test-token')}>Complete Captcha</button>
    </div>
  )),
}));

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const renderWithRouter = (component) => {
  return render(
    <MemoryRouter>
      {component}
    </MemoryRouter>
  );
};

describe('ForgotPasswordPage', () => {
  const mockAuthService = {
    sendForgotPasswordEmail: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock initializeServices to return our mock auth service
    const { initializeServices } = require('../../../services');
    initializeServices.mockResolvedValue({
      authService: mockAuthService,
    });
  });

  it('should render forgot password form', () => {
    renderWithRouter(<ForgotPasswordPage />);

    expect(screen.getByText('Reset Password')).toBeInTheDocument();
    expect(screen.getByText('Enter your email address and we\'ll send you a verification code to reset your password.')).toBeInTheDocument();
    expect(screen.getByLabelText('Email:')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Send Reset Code' })).toBeInTheDocument();
  });

  it('should validate email format', async () => {
    renderWithRouter(<ForgotPasswordPage />);

    const emailInput = screen.getByLabelText('Email:');
    const submitButton = screen.getByRole('button', { name: 'Send Reset Code' });

    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
    });
  });

  it('should require captcha completion', async () => {
    renderWithRouter(<ForgotPasswordPage />);

    const emailInput = screen.getByLabelText('Email:');
    const submitButton = screen.getByRole('button', { name: 'Send Reset Code' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please complete the CAPTCHA.')).toBeInTheDocument();
    });
  });

  it('should handle successful forgot password email submission', async () => {
    mockAuthService.sendForgotPasswordEmail.mockResolvedValue({
      success: true,
      data: { token: 'test-token' }
    });

    renderWithRouter(<ForgotPasswordPage />);

    const emailInput = screen.getByLabelText('Email:');
    const captchaButton = screen.getByText('Complete Captcha');
    const submitButton = screen.getByRole('button', { name: 'Send Reset Code' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(captchaButton);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/otp-verification', {
        state: {
          email: '<EMAIL>',
          verificationToken: 'test-token',
          context: 'forgot_password'
        }
      });
    });
  });

  it('should handle API errors', async () => {
    mockAuthService.sendForgotPasswordEmail.mockResolvedValue({
      success: false,
      error: 'Email not found'
    });

    renderWithRouter(<ForgotPasswordPage />);

    const emailInput = screen.getByLabelText('Email:');
    const captchaButton = screen.getByText('Complete Captcha');
    const submitButton = screen.getByRole('button', { name: 'Send Reset Code' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(captchaButton);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email not found')).toBeInTheDocument();
    });
  });

  it('should navigate to login page when sign in is clicked', () => {
    renderWithRouter(<ForgotPasswordPage />);

    const signInButton = screen.getByText('Sign in');
    fireEvent.click(signInButton);

    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });
});
