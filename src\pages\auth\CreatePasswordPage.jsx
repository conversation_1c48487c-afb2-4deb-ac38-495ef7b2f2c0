import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { initializeServices } from '../../services/index.js';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';
import '../../styles/CreatePassword.css';

const CreatePasswordPage = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';
  const verificationToken = location.state?.verificationToken || '';
  const context = location.state?.context || 'signup'; // 'signup' or 'forgot_password'

  // Password strength validation
  const validatePassword = (password) => {
    const minLength = password.length >= 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
      isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar
    };
  };

  const passwordValidation = validatePassword(password);

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!email) {
      // Navigate back to appropriate page based on context
      navigate(context === 'forgot_password' ? '/forgot-password' : '/signup');
      return;
    }

    if (!authService) {
      setError('Service not initialized. Please refresh the page.');
      return;
    }

    if (!passwordValidation.isValid) {
      setError('Password does not meet the requirements.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    setIsLoading(true);

    try {
      // Debug logging
      console.log('🔐 Setting Password Debug Info:', {
        email,
        hasToken: !!verificationToken,
        tokenLength: verificationToken?.length || 0,
        context: context
      });

      if (context === 'forgot_password') {
        // For forgot password flow, don't implement API call as requested
        console.log('✅ Password reset flow completed (API not implemented)');

        // Navigate to login page with success message
        navigate('/login', {
          state: {
            message: 'Password reset successfully. Please sign in with your new password.'
          }
        });
      } else {
        // Call the setPassword API with the token from OTP validation for signup flow
        const result = await authService.setPassword(
          email,
          password,
          confirmPassword,
          verificationToken
        );

        console.log('📋 Set Password Full Response:', result);
        console.log('📋 Set Password - Data Object:', result.data);
        console.log('📋 Set Password - Nested Data:', result.data?.data);

        if (result.success) {
          // Account created successfully
          console.log('✅ Account created successfully');

          // Navigate to UserDetails page with email
          navigate('/user-details', {
            state: {
              email: email
            }
          });
        } else {
          setError(result.error || 'Failed to create account. Please try again.');
        }
      }
    } catch (error) {
      console.error('Set password error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="create-password-box">
        <h4 className='h4 create-password-title' >
          {context === 'forgot_password' ? 'Reset Your Password' : 'Set A New Password'}
        </h4>


        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="password">New Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."
                required
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword">Confirm Password:</label>
            <div className="password-wrapper">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Enter password..."
                required
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowConfirmPassword((prev) => !prev)}
                aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
              >
                {showConfirmPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
              {confirmPassword && password !== confirmPassword &&(
            <div className="error-message">Passwords do not match.
            </div>
           )}
            </div>
          </div>

          {password && (
            <div className="password-requirements">
              <p className="body4">Password must contain:</p>
              <ul className="requirements-list">
                <li className={passwordValidation.minLength ? 'valid' : 'invalid'}>
                  At least 8 characters
                </li>
                <li className={passwordValidation.hasUpperCase ? 'valid' : 'invalid'}>
                  One uppercase letter
                </li>
                <li className={passwordValidation.hasLowerCase ? 'valid' : 'invalid'}>
                  One lowercase letter
                </li>
                <li className={passwordValidation.hasNumbers ? 'valid' : 'invalid'}>
                  One number
                </li>
                <li className={passwordValidation.hasSpecialChar ? 'valid' : 'invalid'}>
                  One special character
                </li>
              </ul>
            </div>
          )}

          {error && <div className="error-message">{error}</div>}

          <button
            type="submit"
            className="body3-bold login-button"
            disabled={!passwordValidation.isValid || password !== confirmPassword || isLoading}
          >
            {isLoading ? 'Creating Account...' : 'Confirm'}
          </button>
        </form>
      </AnimationBox>
    </div>
  );
};

export default CreatePasswordPage;