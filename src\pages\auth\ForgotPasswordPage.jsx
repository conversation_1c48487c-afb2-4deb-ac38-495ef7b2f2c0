import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { ReCaptcha } from '../../components/ui';
import { initializeServices } from '../../services';
import '../../styles/LoginPage.css';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [captchaError, setCaptchaError] = useState('');
  const [captchaToken, setCaptchaToken] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const recaptchaRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Service initialization failed. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setCaptchaError('');

    let valid = true;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      valid = false;
    }

    if (!captchaToken) {
      setCaptchaError('Please complete the CAPTCHA.');
      valid = false;
    }

    if (!authService) {
      setError('Service not available. Please refresh the page.');
      valid = false;
    }

    if (!valid) return;

    setIsLoading(true);

    try {
      // Call the forgot password email verification API
      const result = await authService.sendForgotPasswordEmail(email, captchaToken);

      if (result.success) {
        // Extract token from response
        const extractedToken = result.data?.data?.token || result.data?.token;

        console.log('📧 Forgot Password Email - Extracted Token:', {
          email,
          token: extractedToken,
          tokenLength: extractedToken?.length || 0,
          hasToken: !!extractedToken,
          tokenSource: result.data?.data?.token ? 'data.data.token' : (result.data?.token ? 'data.token' : 'not found')
        });

        // Navigate to OTP verification screen with forgot password context
        navigate('/otp-verification', {
          state: {
            email,
            verificationToken: extractedToken,
            context: 'forgot_password'
          }
        });
      } else {
        // Show error message
        setError(result.error || 'Please try again.');

        // Reset reCAPTCHA on error
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
          setCaptchaToken(null);
        }
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      setError('An unexpected error occurred. Please try again.');

      // Reset reCAPTCHA on error
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
        setCaptchaToken(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Reset Password</h4>

        <div className="otp-description">
          <p className="body2">Enter your email address and we'll send you a verification code to reset your password.</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error && <div className="error-message">{error}</div>}
          </div>

          <ReCaptcha
            ref={recaptchaRef}
            onChange={(token) => setCaptchaToken(token)}
            showError={false}
          />
          {captchaError && <div className="error-message">{captchaError}</div>}

          <button
            type="submit"
            className="body3-bold login-button"
            disabled={isLoading || !authService}
          >
            {isLoading ? 'Sending...' : 'Send Reset Code'}
          </button>
        </form>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>Remember your password?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/login')}
          >
            Sign in
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default ForgotPasswordPage;
